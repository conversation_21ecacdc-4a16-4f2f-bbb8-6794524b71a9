<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Student Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if($enrollment): ?>
                <!-- Student Info -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Class Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600"><?php echo e($stats['class_stream']); ?></div>
                                <div class="text-sm text-gray-600">Class & Stream</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600"><?php echo e($stats['total_subjects']); ?></div>
                                <div class="text-sm text-gray-600">Total Subjects</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">
                                    <?php echo e($stats['average_grade'] ? number_format($stats['average_grade'], 1) . '%' : 'N/A'); ?>

                                </div>
                                <div class="text-sm text-gray-600">Average Grade</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-600"><?php echo e(date('Y')); ?></div>
                                <div class="text-sm text-gray-600">Academic Year</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Grades -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Grades</h3>
                        <?php if($grades->count() > 0): ?>
                            <div class="bg-blue-50 p-3 rounded mb-4">
                                <p class="text-sm text-blue-700">
                                    📊 Your grades for all subjects. Teachers will update grades as they become available.
                                    Grades marked as "Not assigned" are waiting for teacher assignment.
                                </p>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full table-auto">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letter Grade</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    <?php echo e($grade->subject ? $grade->subject->name : 'Unknown Subject'); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <?php if($grade->grade > 0): ?>
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                            <?php if($grade->grade >= 80): ?> bg-green-100 text-green-800
                                                            <?php elseif($grade->grade >= 60): ?> bg-yellow-100 text-yellow-800
                                                            <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                                            <?php echo e(number_format($grade->grade, 1)); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-gray-400 italic">Not graded</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php if($grade->grade > 0): ?>
                                                        <span class="font-semibold"><?php echo e($grade->letter_grade); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-gray-400">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php if($grade->teacher): ?>
                                                        <?php echo e($grade->teacher->name); ?>

                                                    <?php else: ?>
                                                        <span class="text-gray-400 italic">Not assigned</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php echo e($grade->updated_at ? $grade->updated_at->format('M d, Y') : 'Never'); ?>

                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">No grades available yet. Grades will appear here once your teachers enter them.</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Not Enrolled Message -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Not Enrolled</h3>
                            <p class="mt-1 text-sm text-gray-500"><?php echo e($stats['message']); ?></p>
                            <p class="mt-1 text-sm text-gray-500">Please contact the school administrator for enrollment.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/student/dashboard.blade.php ENDPATH**/ ?>