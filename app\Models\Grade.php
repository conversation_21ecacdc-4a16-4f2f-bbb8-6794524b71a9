<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Grade extends Model
{
    use HasFactory;

    protected $fillable = [
        'enrollment_id',
        'subject_id',
        'teacher_id',
        'grade',
    ];

    protected $casts = [
        'grade' => 'decimal:2',
    ];

    // Relationships
    public function enrollment(): BelongsTo
    {
        return $this->belongsTo(Enrollment::class);
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    // Helper methods
    public function getLetterGradeAttribute(): string
    {
        $grade = $this->grade;

        if ($grade >= 90) return 'A';
        if ($grade >= 80) return 'B+';
        if ($grade >= 70) return 'B';
        if ($grade >= 60) return 'C+';
        if ($grade >= 50) return 'C';
        if ($grade >= 40) return 'D+';
        if ($grade >= 30) return 'D';
        return 'E';
    }

    public function getGradePointAttribute(): float
    {
        $grade = $this->grade;

        if ($grade >= 90) return 4.0;
        if ($grade >= 80) return 3.5;
        if ($grade >= 70) return 3.0;
        if ($grade >= 60) return 2.5;
        if ($grade >= 50) return 2.0;
        if ($grade >= 40) return 1.5;
        if ($grade >= 30) return 1.0;
        return 0.0;
    }

    public function getStudentNameAttribute(): string
    {
        return $this->enrollment->user->name;
    }

    public function getSubjectNameAttribute(): string
    {
        return $this->subject->name;
    }

    public function getTeacherNameAttribute(): string
    {
        return $this->teacher->name;
    }

    // Scopes
    public function scopeForTerm($query, $term)
    {
        return $query->where('term', $term);
    }

    public function scopeForSubject($query, $subjectId)
    {
        return $query->where('subject_id', $subjectId);
    }

    public function scopeForTeacher($query, $teacherId)
    {
        return $query->where('teacher_id', $teacherId);
    }

    public function scopeForStudent($query, $enrollmentId)
    {
        return $query->where('enrollment_id', $enrollmentId);
    }

    // Validation rules
    public static function validationRules(): array
    {
        return [
            'grade' => 'required|numeric|min:0|max:100',
        ];
    }
}
