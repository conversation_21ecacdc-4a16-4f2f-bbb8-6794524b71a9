<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('enrollment_id')->constrained();
            $table->foreignId('subject_id')->constrained();
            $table->foreignId('teacher_id')->constrained('users');
            $table->tinyInteger('term')->default(1);
            $table->decimal('assignment', 5, 2)->default(0);
            $table->decimal('midterm', 5, 2)->default(0);
            $table->decimal('final', 5, 2)->default(0);
            $table->timestamps();
            
            $table->index(['enrollment_id', 'term']);
        });
        
        // Add generated column separately
        DB::statement('ALTER TABLE grades ADD COLUMN total DECIMAL(5,2) 
            GENERATED ALWAYS AS (assignment*0.3 + midterm*0.3 + final*0.4) STORED');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grades');
    }
};
