<!-- Teacher Assignments Tab -->
<div class="space-y-6">
    <!-- Assign Teacher to Subject -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Assign Teacher to Subject</h3>
        <form action="{{ route('admin.assign-teacher') }}" method="POST" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            @csrf
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Teacher</label>
                <select name="teacher_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">Choose Teacher</option>
                    @foreach(\App\Models\User::teachers()->get() as $teacher)
                        <option value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Subject</label>
                <select name="subject_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">Choose Subject</option>
                    @foreach(\App\Models\Subject::all() as $subject)
                        <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded-lg shadow-lg border-2 border-blue-700 mt-6">
                    👨‍🏫 Assign Teacher
                </button>
            </div>
        </form>
    </div>



    <!-- Current Assignments -->
    <div>
        <!-- Subject Teacher Assignments -->
        <div>
            <h3 class="text-lg font-semibold mb-4">Subject Teacher Assignments</h3>
            <div class="border rounded">
                @foreach(\App\Models\SubjectTeacher::with(['user', 'subject'])->get() as $assignment)
                    <div class="p-3 border-b last:border-b-0 flex justify-between items-center">
                        <div>
                            <span class="font-medium">{{ $assignment->user->name }}</span>
                            <span class="text-sm text-gray-500 block">{{ $assignment->subject->name }}</span>
                        </div>
                        <form action="{{ route('admin.remove-teacher-subject') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="teacher_id" value="{{ $assignment->user_id }}">
                            <input type="hidden" name="subject_id" value="{{ $assignment->subject_id }}">
                            <button type="submit" class="text-red-600 hover:text-red-900"
                                    onclick="return confirm('Are you sure?')">Remove</button>
                        </form>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button onclick="assignAllTeachersToSubjects()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                Auto-Assign Teachers
            </button>
            <button onclick="showAssignmentTips()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                Assignment Tips
            </button>
            <button onclick="resetAssignmentForms()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                Reset Forms
            </button>
        </div>
    </div>
</div>

<script>
function assignAllTeachersToSubjects() {
    alert('Assignment Tips:\n\n1. Create teachers and subjects first\n2. Use the form above to assign teachers to subjects\n\nFor your school project, manual assignment gives you better control.');
}

function showAssignmentTips() {
    alert('Teacher Assignment Guide:\n\n📚 Subject Teachers:\n- Select a teacher\n- Choose a subject\n- Click "Assign Teacher"\n\nEach teacher can teach multiple subjects!');
}

function resetAssignmentForms() {
    document.querySelectorAll('form').forEach(form => form.reset());
}
</script>
