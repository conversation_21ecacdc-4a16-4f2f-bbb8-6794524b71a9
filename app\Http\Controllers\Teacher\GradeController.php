<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Enrollment;
use App\Models\SubjectTeacher;
use Illuminate\Http\Request;

class GradeController extends Controller
{
    public function index(Request $request)
    {
        $teacher = auth()->user();

        // Get teacher's assigned subjects
        $assignedSubjects = SubjectTeacher::with('subject')
            ->where('user_id', $teacher->id)
            ->get();

        $selectedSubject = $request->get('subject');

        $grades = collect();
        $enrollments = collect();

        if ($selectedSubject) {
            // Get enrollments that have grade records for this subject
            $enrollments = Enrollment::with(['user', 'schoolClass', 'stream'])
                ->whereHas('grades', function($query) use ($selectedSubject) {
                    $query->where('subject_id', $selectedSubject);
                })
                ->get();

            // Get existing grades for this subject
            $grades = Grade::with(['enrollment.user'])
                ->where('subject_id', $selectedSubject)
                ->get()
                ->keyBy('enrollment_id');
        }

        return view('teacher.grades.index', compact(
            'assignedSubjects', 'selectedSubject', 'enrollments', 'grades'
        ));
    }

    public function store(Request $request)
    {
        $request->validate([
            'enrollment_id' => 'required|exists:enrollments,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade' => 'required|numeric|min:0|max:100',
        ]);

        Grade::updateOrCreate(
            [
                'enrollment_id' => $request->enrollment_id,
                'subject_id' => $request->subject_id,
            ],
            [
                'teacher_id' => auth()->id(),
                'grade' => $request->grade,
            ]
        );

        return redirect()->back()->with('success', 'Grade saved successfully!');
    }
}
