<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Admin Dashboard
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <!-- Simple Tab Navigation -->
            <div class="bg-white shadow rounded-lg">
                <div class="border-b border-gray-200">
                    <nav class="flex px-6">
                        <button onclick="showTab('users')" id="users-tab" class="tab-button py-4 px-6 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Users
                        </button>
                        <button onclick="showTab('classes')" id="classes-tab" class="tab-button py-4 px-6 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Classes
                        </button>
                        <button onclick="showTab('subjects')" id="subjects-tab" class="tab-button py-4 px-6 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Subjects
                        </button>
                        <button onclick="showTab('enrollments')" id="enrollments-tab" class="tab-button py-4 px-6 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Enrollments
                        </button>
                        <button onclick="showTab('assignments')" id="assignments-tab" class="tab-button py-4 px-6 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Assignments
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Users Tab -->
                    <div id="users-content" class="tab-content hidden">
                        @include('admin.tabs.users_simple')
                    </div>

                    <!-- Classes Tab -->
                    <div id="classes-content" class="tab-content hidden">
                        @include('admin.tabs.classes_simple')
                    </div>

                    <!-- Subjects Tab -->
                    <div id="subjects-content" class="tab-content hidden">
                        @include('admin.tabs.subjects_simple')
                    </div>

                    <!-- Enrollments Tab -->
                    <div id="enrollments-content" class="tab-content hidden">
                        @include('admin.tabs.enrollments_simple')
                    </div>

                    <!-- Assignments Tab -->
                    <div id="assignments-content" class="tab-content hidden">
                        @include('admin.tabs.assignments_simple')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Show first tab by default
        document.addEventListener('DOMContentLoaded', function() {
            showTab('users');
        });
    </script>
</x-app-layout>


