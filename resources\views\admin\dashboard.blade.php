<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('School Management System - Admin Panel') }}
        </h2>
    </x-slot>

    <div class="py-4">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Compact Statistics Bar -->
            <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
                <div class="flex flex-wrap justify-between items-center text-sm">
                    <div class="flex items-center space-x-1">
                        <span class="text-blue-600 font-bold text-lg">{{ $stats['total_students'] }}</span>
                        <span class="text-gray-600">Students</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="text-green-600 font-bold text-lg">{{ $stats['total_teachers'] }}</span>
                        <span class="text-gray-600">Teachers</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="text-purple-600 font-bold text-lg">{{ $stats['total_classes'] }}</span>
                        <span class="text-gray-600">Classes</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="text-orange-600 font-bold text-lg">{{ $stats['total_subjects'] }}</span>
                        <span class="text-gray-600">Subjects</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="text-red-600 font-bold text-lg">{{ $stats['current_enrollments'] }}</span>
                        <span class="text-gray-600">Enrolled</span>
                    </div>
                </div>
            </div>

            <!-- Smart Alerts Section -->
            @if(count($alerts) > 0)
                <div class="mb-4 space-y-2">
                    @foreach($alerts as $alert)
                        <div class="p-3 rounded-lg border-l-4
                            @if($alert['type'] === 'error') bg-red-50 border-red-400 text-red-700
                            @elseif($alert['type'] === 'warning') bg-yellow-50 border-yellow-400 text-yellow-700
                            @else bg-blue-50 border-blue-400 text-blue-700 @endif">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-sm">{{ $alert['title'] }}</h4>
                                    <p class="text-sm mt-1">{{ $alert['message'] }}</p>
                                    <p class="text-xs mt-1 opacity-75">{{ $alert['action'] }}</p>
                                </div>
                                <button onclick="this.parentElement.parentElement.style.display='none'"
                                        class="text-gray-400 hover:text-gray-600 ml-2">×</button>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Tabbed Interface -->
            <div class="bg-white shadow rounded-lg">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="showTab('users')" id="users-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Users
                        </button>
                        <button onclick="showTab('classes')" id="classes-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Classes & Streams
                        </button>
                        <button onclick="showTab('subjects')" id="subjects-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Subjects
                        </button>
                        <button onclick="showTab('enrollments')" id="enrollments-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Student Enrollments
                        </button>
                        <button onclick="showTab('assignments')" id="assignments-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Teacher Assignments
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Users Tab -->
                    <div id="users-content" class="tab-content hidden">
                        @include('admin.tabs.users')
                    </div>

                    <!-- Classes Tab -->
                    <div id="classes-content" class="tab-content hidden">
                        @include('admin.tabs.classes')
                    </div>

                    <!-- Subjects Tab -->
                    <div id="subjects-content" class="tab-content hidden">
                        @include('admin.tabs.subjects')
                    </div>

                    <!-- Enrollments Tab -->
                    <div id="enrollments-content" class="tab-content hidden">
                        @include('admin.tabs.enrollments')
                    </div>

                    <!-- Assignments Tab -->
                    <div id="assignments-content" class="tab-content hidden">
                        @include('admin.tabs.assignments')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Show first tab by default
        document.addEventListener('DOMContentLoaded', function() {
            showTab('users');
        });
    </script>
</x-app-layout>


