<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Manage Grades') }}
        </h2>
    </x-slot>

    <style>
        /* Ensure buttons are always visible */
        .grade-save-btn {
            background-color: #059669 !important;
            color: white !important;
            font-weight: bold !important;
            padding: 8px 16px !important;
            border-radius: 8px !important;
            border: 2px solid #047857 !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        .grade-save-btn:hover {
            background-color: #047857 !important;
        }
    </style>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Subject and Term Selection -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('teacher.grades.index') }}" class="flex gap-4 items-end">
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                            <select name="subject" id="subject" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select Subject</option>
                                @foreach($assignedSubjects as $assignment)
                                    <option value="{{ $assignment->subject->id }}" {{ $selectedSubject == $assignment->subject->id ? 'selected' : '' }}>
                                        {{ $assignment->subject->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>



                        <button type="submit" class="bg-blue-600 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded-lg shadow-lg border-2 border-blue-700">
                            📚 Load Students
                        </button>
                    </form>
                </div>
            </div>

            @if($selectedSubject && $enrollments->count() > 0)
                <!-- Grade Entry Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">Enter Grades</h3>
                        <p class="text-sm text-gray-600 mb-4">📝 Enter a grade (0-100) for each student and click the green ✅ Save button for each row</p>

                        @if($enrollments->count() > 0)
                            <div class="bg-blue-50 p-3 rounded mb-4">
                                <p class="text-sm text-blue-700">
                                    📊 Found {{ $enrollments->count() }} students with grade records for this subject.
                                    Each row has a Save button in the last column.
                                </p>
                            </div>
                        @endif

                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade (0-100)</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letter Grade</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-green-600 uppercase tracking-wider bg-green-50">Save Grades</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($enrollments as $enrollment)
                                        @php
                                            $existingGrade = $grades->get($enrollment->id);
                                        @endphp
                                        <tr>
                                            <form action="{{ route('teacher.grades.store') }}" method="POST" class="contents">
                                                @csrf
                                                <input type="hidden" name="enrollment_id" value="{{ $enrollment->id }}">
                                                <input type="hidden" name="subject_id" value="{{ $selectedSubject }}">


                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ $enrollment->user->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $enrollment->schoolClass->name }} {{ $enrollment->stream->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="number" name="grade" min="0" max="100" step="0.1"
                                                           value="{{ $existingGrade && $existingGrade->grade > 0 ? $existingGrade->grade : '' }}"
                                                           class="w-24 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    @if($existingGrade && $existingGrade->grade > 0)
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                            @if($existingGrade->grade >= 80) bg-green-100 text-green-800
                                                            @elseif($existingGrade->grade >= 60) bg-yellow-100 text-yellow-800
                                                            @else bg-red-100 text-red-800 @endif">
                                                            {{ $existingGrade->letter_grade }}
                                                        </span>
                                                    @else
                                                        <span class="text-gray-400">No grade yet</span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap bg-green-50">
                                                    <button type="submit" class="grade-save-btn w-full text-sm">
                                                        ✅ SAVE GRADE
                                                    </button>
                                                </td>
                                            </form>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @elseif($selectedSubject)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center text-gray-500">
                        No students enrolled for this subject.
                    </div>
                </div>
            @else
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center text-gray-500">
                        Please select a subject to view students and enter grades.
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
