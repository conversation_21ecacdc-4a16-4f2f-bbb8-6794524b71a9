<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Handle the incoming request and redirect to appropriate dashboard
     */
    public function __invoke(Request $request)
    {
        $user = auth()->user();

        // Redirect based on user role
        switch ($user->role) {
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'teacher':
                // Check if teacher is also a class teacher
                $isClassTeacher = \App\Models\ClassTeacher::where('user_id', $user->id)
                    ->where('academic_year', date('Y'))
                    ->exists();

                if ($isClassTeacher) {
                    return redirect()->route('class_teacher.dashboard');
                }
                return redirect()->route('teacher.dashboard');
            case 'student':
                return redirect()->route('student.dashboard');
            default:
                abort(403, 'Invalid user role');
        }
    }
}
