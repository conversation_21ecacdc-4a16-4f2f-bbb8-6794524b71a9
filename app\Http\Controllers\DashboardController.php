<?php

namespace App\Http\Controllers;

class DashboardController extends Controller
{
    /**
     * Handle the incoming request and redirect to appropriate dashboard
     */
    public function __invoke()
    {
        $user = auth()->user();

        // Redirect based on user role
        switch ($user->role) {
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'teacher':
                return redirect()->route('teacher.dashboard');
            case 'student':
                return redirect()->route('student.dashboard');
            default:
                abort(403, 'Invalid user role');
        }
    }
}
