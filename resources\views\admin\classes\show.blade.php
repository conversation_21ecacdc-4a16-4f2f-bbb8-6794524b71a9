<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Class Details: ') . $class->name }}
            </h2>
            <div class="space-x-2">
                <a href="{{ route('admin.classes.edit', $class) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Class
                </a>
                <a href="{{ route('admin.classes.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Classes
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Class Info -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Class Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded">
                            <h4 class="font-semibold text-blue-800">Class Name</h4>
                            <p class="text-2xl font-bold text-blue-600">{{ $class->name }}</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded">
                            <h4 class="font-semibold text-green-800">Total Streams</h4>
                            <p class="text-2xl font-bold text-green-600">{{ $class->streams->count() }}</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded">
                            <h4 class="font-semibold text-purple-800">Total Students</h4>
                            <p class="text-2xl font-bold text-purple-600">{{ $class->enrollments->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Streams -->
            @if($class->streams->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Streams in {{ $class->name }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @foreach($class->streams as $stream)
                            <div class="border rounded-lg p-4">
                                <h4 class="font-semibold text-lg">{{ $class->name }} {{ $stream->name }}</h4>
                                <p class="text-gray-600">{{ $stream->enrollments->count() }} students enrolled</p>
                                
                                @if($stream->enrollments->count() > 0)
                                    <div class="mt-2">
                                        <h5 class="font-medium text-sm text-gray-700">Students:</h5>
                                        <ul class="text-sm text-gray-600">
                                            @foreach($stream->enrollments->take(5) as $enrollment)
                                                <li>• {{ $enrollment->user->name }}</li>
                                            @endforeach
                                            @if($stream->enrollments->count() > 5)
                                                <li class="text-gray-500">... and {{ $stream->enrollments->count() - 5 }} more</li>
                                            @endif
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- All Students in Class -->
            @if($class->enrollments->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">All Students in {{ $class->name }}</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Student Name
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stream
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Academic Year
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($class->enrollments as $enrollment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $enrollment->user->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $class->name }} {{ $enrollment->stream->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $enrollment->academic_year }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif

        </div>
    </div>
</x-app-layout>
