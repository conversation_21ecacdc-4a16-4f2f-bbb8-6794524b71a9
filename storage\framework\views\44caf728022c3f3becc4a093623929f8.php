<!-- Subjects Management Tab -->
<div class="space-y-6">
    <!-- Add Subject Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New Subject</h3>
        <form action="<?php echo e(route('admin.create-subject')); ?>" method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm font-medium text-gray-700">Subject Name</label>
                <input type="text" name="name" placeholder="e.g., Mathematics, Science, English" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-orange-600 hover:bg-orange-800 text-white font-bold py-3 px-6 rounded-lg shadow-lg border-2 border-orange-700 mt-6">
                    📖 Add Subject
                </button>
            </div>
        </form>
    </div>

    <!-- Subjects List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">All Subjects</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Subject Name</th>
                        <th class="px-4 py-2 text-left">Assigned Teachers</th>
                        <th class="px-4 py-2 text-left">Total Grades</th>
                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = \App\Models\Subject::withCount(['subjectTeachers', 'grades'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-t">
                            <td class="px-4 py-2 font-medium"><?php echo e($subject->name); ?></td>
                            <td class="px-4 py-2"><?php echo e($subject->subject_teachers_count); ?> teachers</td>
                            <td class="px-4 py-2"><?php echo e($subject->grades_count); ?> grades</td>
                            <td class="px-4 py-2">
                                <button onclick="editSubject(<?php echo e($subject->id); ?>, '<?php echo e($subject->name); ?>')" class="text-blue-600 hover:text-blue-900 mr-2">Edit</button>
                                <form action="<?php echo e(route('admin.delete', ['type' => 'subject', 'id' => $subject->id])); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900"
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions - Core Subjects -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Add Core Subjects</h3>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            <button onclick="createQuickSubject('Mathematics')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-3 rounded text-sm">
                🔢 Mathematics
            </button>
            <button onclick="createQuickSubject('English')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-3 rounded text-sm">
                🇬🇧 English
            </button>
            <button onclick="createQuickSubject('Kiswahili')" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded text-sm">
                🇰🇪 Kiswahili
            </button>
            <button onclick="createQuickSubject('Science')" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-3 rounded text-sm">
                🔬 Science
            </button>
            <button onclick="createQuickSubject('Social Studies')" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-3 rounded text-sm">
                🌍 Social Studies
            </button>
        </div>
        <div class="mt-3">
            <button onclick="createAllCoreSubjects()" class="bg-gray-600 hover:bg-gray-800 text-white font-bold py-2 px-4 rounded text-sm">
                ✨ Create All 5 Core Subjects
            </button>
        </div>
    </div>
</div>

<script>
function createQuickSubject(subjectName) {
    document.querySelector('input[name="name"]').value = subjectName;
}

function createAllCoreSubjects() {
    const coreSubjects = ['Mathematics', 'English', 'Kiswahili', 'Science', 'Social Studies'];
    alert('Will create 5 core subjects: ' + coreSubjects.join(', ') + '\n\nClick each subject button and submit to create them one by one.');
}

function editSubject(id, name) {
    document.querySelector('input[name="name"]').value = name;
    alert('Subject name loaded in form. Modify and submit to update.');
}
</script>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/subjects.blade.php ENDPATH**/ ?>