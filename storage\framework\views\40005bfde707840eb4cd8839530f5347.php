<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Class Teacher Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if($classTeacher): ?>
                <!-- Class Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Class: <?php echo e($stats['class_stream']); ?></h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600"><?php echo e($stats['total_students']); ?></div>
                                <div class="text-sm text-gray-600">Total Students</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600"><?php echo e($stats['students_with_grades']); ?></div>
                                <div class="text-sm text-gray-600">Students with Grades</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-purple-600">
                                    <?php echo e($stats['class_average'] ? number_format($stats['class_average'], 1) . '%' : 'N/A'); ?>

                                </div>
                                <div class="text-sm text-gray-600">Class Average</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-orange-600"><?php echo e(date('Y')); ?></div>
                                <div class="text-sm text-gray-600">Academic Year</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students List -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Students</h3>
                        <?php if($students->count() > 0): ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full table-auto">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student Name</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subjects</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Grade</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $studentAverage = $student->grades->avg('total');
                                                $subjectCount = $student->grades->groupBy('subject_id')->count();
                                            ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    <?php echo e($student->user->name); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php echo e($student->user->email); ?>

                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php echo e($subjectCount); ?> subjects
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php if($studentAverage): ?>
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                            <?php if($studentAverage >= 80): ?> bg-green-100 text-green-800
                                                            <?php elseif($studentAverage >= 60): ?> bg-yellow-100 text-yellow-800
                                                            <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                                            <?php echo e(number_format($studentAverage, 1)); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-gray-400">No grades</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php if($studentAverage): ?>
                                                        <?php if($studentAverage >= 80): ?>
                                                            <span class="text-green-600 font-semibold">Excellent</span>
                                                        <?php elseif($studentAverage >= 70): ?>
                                                            <span class="text-blue-600 font-semibold">Good</span>
                                                        <?php elseif($studentAverage >= 60): ?>
                                                            <span class="text-yellow-600 font-semibold">Average</span>
                                                        <?php else: ?>
                                                            <span class="text-red-600 font-semibold">Needs Improvement</span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-gray-400">Not assessed</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">No students enrolled in your class.</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Not Assigned Message -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h4m6 0h4M7 15h10" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Not Assigned as Class Teacher</h3>
                            <p class="mt-1 text-sm text-gray-500"><?php echo e($stats['message']); ?></p>
                            <p class="mt-1 text-sm text-gray-500">Please contact the administrator for class teacher assignment.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/class-teacher/dashboard.blade.php ENDPATH**/ ?>