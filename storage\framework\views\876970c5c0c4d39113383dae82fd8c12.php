<!-- Users Management Tab -->
<div class="space-y-6">
    <!-- Add User Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New User</h3>
        <p class="text-sm text-gray-600 mb-4">🎓 Students automatically get grade records for all subjects when enrolled in a class!</p>
        <form action="<?php echo e(route('admin.create-user')); ?>" method="POST" id="userForm">
            <?php echo csrf_field(); ?>

            <!-- Basic User Info -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" name="name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role" id="userRole" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" onchange="toggleAssignmentFields()">
                        <option value="">Select Role</option>
                        <option value="admin">Admin</option>
                        <option value="teacher">Teacher</option>
                        <option value="student">Student</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" name="password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
            </div>

            <!-- Student Assignment Fields (Hidden by default) -->
            <div id="studentFields" class="hidden bg-blue-50 p-4 rounded mb-4">
                <h4 class="font-medium mb-3 text-blue-800">Student Class Assignment</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Select Class</label>
                        <select name="class_id" id="studentClassId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="">Choose Class</option>
                            <?php $__currentLoopData = \App\Models\SchoolClass::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($class->id); ?>"><?php echo e($class->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Select Stream</label>
                        <select name="stream_id" id="studentStreamId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="">First select class</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Academic Year</label>
                        <input type="number" name="academic_year" value="<?php echo e(date('Y')); ?>" min="2020" max="2030" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                </div>
            </div>

            <!-- Teacher Assignment Fields (Hidden by default) -->
            <div id="teacherFields" class="hidden bg-green-50 p-4 rounded mb-4">
                <h4 class="font-medium mb-3 text-green-800">Teacher Subject Assignment</h4>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Select Subjects (Hold Ctrl to select multiple)</label>
                    <select name="subject_ids[]" multiple class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" size="5">
                        <?php $__currentLoopData = \App\Models\Subject::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($subject->id); ?>"><?php echo e($subject->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Hold Ctrl (or Cmd on Mac) and click to select multiple subjects</p>
                </div>
            </div>

            <div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded-lg shadow-lg border-2 border-blue-700">
                    ✅ Create User & Assign
                </button>
            </div>
        </form>
    </div>

    <!-- Users List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">All Users</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Name</th>
                        <th class="px-4 py-2 text-left">Email</th>
                        <th class="px-4 py-2 text-left">Role</th>
                        <th class="px-4 py-2 text-left">Status</th>
                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = \App\Models\User::with(['enrollments.grades', 'subjectTeachers'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-t">
                            <td class="px-4 py-2"><?php echo e($user->name); ?></td>
                            <td class="px-4 py-2"><?php echo e($user->email); ?></td>
                            <td class="px-4 py-2">
                                <span class="px-2 py-1 text-xs rounded
                                    <?php if($user->role === 'admin'): ?> bg-red-100 text-red-800
                                    <?php elseif($user->role === 'teacher'): ?> bg-blue-100 text-blue-800
                                    <?php else: ?> bg-green-100 text-green-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($user->role)); ?>

                                </span>
                            </td>
                            <td class="px-4 py-2">
                                <?php if($user->role === 'student'): ?>
                                    <?php if($user->enrollments->count() > 0): ?>
                                        <span class="px-2 py-1 text-xs rounded bg-green-100 text-green-800">
                                            ✅ Enrolled (<?php echo e($user->enrollments->first()->grades->count()); ?> grades)
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs rounded bg-red-100 text-red-800">
                                            ❌ Not Enrolled
                                        </span>
                                    <?php endif; ?>
                                <?php elseif($user->role === 'teacher'): ?>
                                    <?php if($user->subjectTeachers->count() > 0): ?>
                                        <span class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                                            📚 <?php echo e($user->subjectTeachers->count()); ?> subjects
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800">
                                            ⚠️ No subjects
                                        </span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">
                                        👑 Admin
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-2">
                                <button onclick="editUser(<?php echo e($user->id); ?>, '<?php echo e($user->name); ?>', '<?php echo e($user->email); ?>', '<?php echo e($user->role); ?>')" class="text-blue-600 hover:text-blue-900 mr-2">Edit</button>
                                <?php if($user->id !== auth()->id()): ?>
                                    <form action="<?php echo e(route('admin.delete', ['type' => 'user', 'id' => $user->id])); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                                onclick="return confirm('Are you sure?')">Delete</button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button onclick="createQuickUser('admin')" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Admin
            </button>
            <button onclick="createQuickUser('teacher')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Teacher
            </button>
            <button onclick="createQuickUser('student')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Student
            </button>
            <button onclick="resetForm()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                Reset Form
            </button>
            <button onclick="fixUnenrolledStudents()" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded text-sm">
                🔧 Fix Unenrolled Students
            </button>
        </div>
    </div>
</div>

<script>
function toggleAssignmentFields() {
    const role = document.getElementById('userRole').value;
    const studentFields = document.getElementById('studentFields');
    const teacherFields = document.getElementById('teacherFields');

    // Hide all fields first
    studentFields.classList.add('hidden');
    teacherFields.classList.add('hidden');

    // Show relevant fields based on role
    if (role === 'student') {
        studentFields.classList.remove('hidden');
    } else if (role === 'teacher') {
        teacherFields.classList.remove('hidden');
    }
}

// Handle class selection for students
document.getElementById('studentClassId').addEventListener('change', function() {
    const classId = this.value;
    const streamSelect = document.getElementById('studentStreamId');

    streamSelect.innerHTML = '<option value="">Loading...</option>';

    if (classId) {
        const classData = <?php echo json_encode(\App\Models\SchoolClass::with('streams')->get(), 15, 512) ?>;
        const selectedClass = classData.find(c => c.id == classId);

        streamSelect.innerHTML = '<option value="">Choose Stream</option>';

        if (selectedClass && selectedClass.streams) {
            selectedClass.streams.forEach(stream => {
                const option = document.createElement('option');
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
            });
        }
    } else {
        streamSelect.innerHTML = '<option value="">First select class</option>';
    }
});

// Quick user creation functions
function createQuickUser(role) {
    document.getElementById('userRole').value = role;
    toggleAssignmentFields();

    // Pre-fill some demo data
    const nameField = document.querySelector('input[name="name"]');
    const emailField = document.querySelector('input[name="email"]');
    const passwordField = document.querySelector('input[name="password"]');

    const timestamp = Date.now();
    nameField.value = `${role.charAt(0).toUpperCase() + role.slice(1)} ${timestamp.toString().slice(-4)}`;
    emailField.value = `${role}${timestamp.toString().slice(-4)}@school.com`;
    passwordField.value = 'password123';
}

function resetForm() {
    document.getElementById('userForm').reset();
    toggleAssignmentFields();
}

function editUser(id, name, email, role) {
    document.querySelector('input[name="name"]').value = name;
    document.querySelector('input[name="email"]').value = email;
    document.getElementById('userRole').value = role;
    document.querySelector('input[name="password"]').value = 'password123';
    toggleAssignmentFields();
    alert('User details loaded in form. Modify and submit to update.');
}

function fixUnenrolledStudents() {
    alert('This will help you identify and fix students who are not enrolled in any class.\n\nLook for students with "❌ Not Enrolled" status and use the Enrollments tab to enroll them manually.\n\nFor your school project, this shows the system is smart enough to track enrollment status!');
}
</script>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/users.blade.php ENDPATH**/ ?>