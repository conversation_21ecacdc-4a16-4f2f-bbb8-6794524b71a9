<?php

namespace Database\Seeders;

use App\Models\Enrollment;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\SubjectTeacher;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GradeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create some subject-teacher assignments
        $teachers = User::teachers()->get();
        $subjects = Subject::all();

        // Assign each teacher to 1-2 subjects
        foreach ($teachers as $index => $teacher) {
            $subjectIndex = $index % $subjects->count();
            SubjectTeacher::create([
                'user_id' => $teacher->id,
                'subject_id' => $subjects[$subjectIndex]->id,
            ]);

            // Some teachers get a second subject
            if ($index % 3 == 0 && $subjects->count() > $subjectIndex + 1) {
                SubjectTeacher::create([
                    'user_id' => $teacher->id,
                    'subject_id' => $subjects[$subjectIndex + 1]->id,
                ]);
            }
        }

        // Create sample grades for enrolled students
        $enrollments = Enrollment::with('user')->get();
        $subjectTeachers = SubjectTeacher::with(['user', 'subject'])->get();

        foreach ($enrollments as $enrollment) {
            // Create grades for 3-4 subjects per student
            $assignedSubjects = $subjectTeachers->random(rand(3, 4));

            foreach ($assignedSubjects as $subjectTeacher) {
                // Create grades for terms 1 and 2 (current academic year)
                for ($term = 1; $term <= 2; $term++) {
                    Grade::create([
                        'enrollment_id' => $enrollment->id,
                        'subject_id' => $subjectTeacher->subject->id,
                        'teacher_id' => $subjectTeacher->user->id,
                        'term' => $term,
                        'assignment' => rand(60, 95),
                        'midterm' => rand(55, 90),
                        'final' => rand(65, 95),
                    ]);
                }
            }
        }
    }
}
