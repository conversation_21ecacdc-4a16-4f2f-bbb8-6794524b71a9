<!-- Add User Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Add New User</h3>
    <form action="{{ route('admin.create-user') }}" method="POST" class="bg-gray-50 p-4 rounded">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select name="role" id="user_role" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Select Role</option>
                    <option value="admin">Admin</option>
                    <option value="teacher">Teacher</option>
                    <option value="student">Student</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
        </div>
        
        <!-- Student fields -->
        <div id="student-fields" class="hidden grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select name="class_id" id="user_class_id" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Class</option>
                    @foreach(\App\Models\SchoolClass::all() as $class)
                        <option value="{{ $class->id }}">{{ $class->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Stream</label>
                <select name="stream_id" id="user_stream_id" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">First select class</option>
                </select>
            </div>
        </div>
        
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
            Create User
        </button>
    </form>
</div>

<!-- Users Table -->
<div>
    <h3 class="text-lg font-semibold mb-4">All Users</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Role</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Class/Stream</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach(\App\Models\User::with(['enrollments.schoolClass', 'enrollments.stream'])->get() as $user)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            {{ $user->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $user->email }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                @if($user->role === 'admin') bg-red-100 text-red-800
                                @elseif($user->role === 'teacher') bg-green-100 text-green-800
                                @else bg-blue-100 text-blue-800 @endif">
                                {{ ucfirst($user->role) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            @if($user->role === 'student' && $user->enrollments->count() > 0)
                                {{ $user->enrollments->first()->schoolClass->name ?? 'N/A' }} 
                                {{ $user->enrollments->first()->stream->name ?? '' }}
                            @else
                                -
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <button onclick="editUser({{ $user->id }}, '{{ $user->name }}', '{{ $user->email }}', '{{ $user->role }}')" 
                                    class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs mr-2">
                                Edit
                            </button>
                            <form action="{{ route('admin.delete', ['type' => 'user', 'id' => $user->id]) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('Are you sure?')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<script>
// Show/hide student fields based on role selection
document.getElementById('user_role').addEventListener('change', function() {
    const studentFields = document.getElementById('student-fields');
    if (this.value === 'student') {
        studentFields.classList.remove('hidden');
    } else {
        studentFields.classList.add('hidden');
    }
});

// Load streams when class is selected
document.getElementById('user_class_id').addEventListener('change', function() {
    const classId = this.value;
    const streamSelect = document.getElementById('user_stream_id');
    
    streamSelect.innerHTML = '<option value="">Loading...</option>';
    
    if (classId) {
        const classData = @json(\App\Models\SchoolClass::with('streams')->get());
        const selectedClass = classData.find(c => c.id == classId);
        
        streamSelect.innerHTML = '<option value="">Choose Stream</option>';
        
        if (selectedClass && selectedClass.streams) {
            selectedClass.streams.forEach(stream => {
                const option = document.createElement('option');
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
            });
        }
    } else {
        streamSelect.innerHTML = '<option value="">First select class</option>';
    }
});

function editUser(id, name, email, role) {
    // Simple edit functionality - you can expand this
    const newName = prompt('Edit name:', name);
    const newEmail = prompt('Edit email:', email);
    
    if (newName && newEmail) {
        // You can add an AJAX call here to update the user
        alert('Edit functionality can be implemented with AJAX');
    }
}
</script>
