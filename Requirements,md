Here's the **complete, step-by-step flow logic** for your school results management system, structured for clarity and implementation:

---

### **1. System Initialization (Admin)**
```mermaid
flowchart TD
    A[Admin Logs In] --> B[Create Users]
    B --> B1[Add Teachers]
    B --> B2[Add Students]
    A --> C[Setup Structure]
    C --> C1[Create Classes 1-8]
    C1 --> C2[Add Streams A/B/C per Class]
    C --> C3[Define Subjects: Math, Science, etc.]
    A --> D[Assign Roles]
    D --> D1[Link Teachers to Subjects]
    D --> D2[Appoint Class Teachers to Class-Streams]
    A --> E[Enroll Students]
    E --> E1[Assign to Class-Stream]
```

**Rules**:
- All structure must exist before assignments
- A teacher must be assigned to a subject before teaching it

---

### **2. Grade Entry Flow (Subject Teacher)**
```mermaid
flowchart TD
    T[Teacher Logs In] --> T1[View Assigned Subjects]
    T1 --> T2[Select Subject]
    T2 --> T3[Select Term 1/2/3]
    T3 --> T4[View Class List]
    T4 --> T5[Enter Marks]
    T5 --> T51[Assignment 30%]
    T5 --> T52[Midterm 30%]
    T5 --> T53[Final 40%]
    T5 --> T6[Auto-Calculate Total]
    T6 --> T7[Save to Database]
```

**Validation**:
- Scores: 0-100 with 2 decimal places
- Term must be current or past (no future terms)
- Teacher can only edit their subject's grades

---

### **3. Class Monitoring (Class Teacher)**
```mermaid
flowchart TD
    CT[Class Teacher Logs In] --> CT1[View Assigned Class-Stream]
    CT1 --> CT2[Select Term]
    CT2 --> CT3[View All Subjects]
    CT3 --> CT4[See Class Averages]
    CT4 --> CT41[Math: 78%]
    CT4 --> CT42[Science: 82%]
    CT3 --> CT5[Identify Weak Students]
    CT5 --> CT51[Alert if <60%]
```

**Data Filter**:
```sql
WHERE class_id = {their_class} 
AND stream_id = {their_stream}
AND term = {selected_term}
```

---

### **4. Student/Parent Flow**
```mermaid
flowchart TD
    S[Student Logs In] --> S1[Select Term]
    S1 --> S2[View Grades Table]
    S2 --> S21[Math: A (85%)]
    S2 --> S22[Science: B+ (78%)]
    S1 --> S3[Print Report]
```

**Security**:
```php
// Always filters by:
where('enrollment.user_id', auth()->id())
```

---

### **5. Data Relationships**
```mermaid
erDiagram
    users ||--o{ subject_teachers : "teaches"
    users ||--o{ class_teachers : "manages"
    users ||--o{ enrollments : "studies"
    classes ||--o{ streams : "has"
    enrollments }o--|| streams : "in"
    enrollments ||--o{ grades : "has"
    subject_teachers }o--|| subjects : "for"
    grades }o--|| subjects : "in"
```

---

### **6. Edge Case Handling**
| Scenario | Solution |
|----------|----------|
| Teacher tries editing wrong subject | 403 Forbidden |
| Student views other's grades | Returns empty set |
| Admin enters grades | UI hidden by role |
| Invalid term selected | Default to Term 1 |

---

### **7. Key Business Rules**
1. **Grade Calculation**:
   ```php
   $total = ($assignment * 0.3) + ($midterm * 0.3) + ($final * 0.4);
   ```
2. **Promotion Logic**:
   ```sql
   INSERT INTO enrollments (user_id, class_id+1, stream_id, year+1)
   SELECT user_id, class_id, stream_id, year FROM enrollments 
   WHERE year = CURRENT_YEAR;
   ```
3. **Term Transition**:
   ```php
   $currentTerm = (date('m') < 5) ? 1 : ((date('m') < 9) ? 2 : 3);
   ```

---

### **8. Full Data Flow**
1. **Admin** creates structure (users → classes → assignments)
2. **Subject Teachers** populate grade data
3. **Class Teachers** monitor aggregated results
4. **Students** consume their personal data
5. **System** auto-calculates totals and terms

---

This is the **complete unbroken logic** of your application. Every user action, data validation, and system response is accounted for without shortcuts. Would you like me to elaborate on any specific flow segment?



admin

class teacher

teachers

students/parent (parents to use student logins to see results)

LOGIC

admin

does user CRUD, 

CRUD for classes (classes may have many streams)

assigns teachers to subject, 

assigns student to a class which,

teacher already assigned to subject is valid to be chosen for a class subject teacher. so all students in that class subsequently will be taught by  that teacher in that subject ,

he appoints class teacher, 

class teacher 

can only see his class results for all class students for all subjects.

teacher 

can CRUD subjects students results

students 

can only see their results for all their subjects

the database schema is the most important thing for the app to be functional

we assume of primary school so all students from class one to 8  do the same subjects, Math, science, English, Kiswahili, Social studies.