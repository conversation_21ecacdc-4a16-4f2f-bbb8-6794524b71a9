<!-- Add Subject Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Add New Subject</h3>
    <form action="<?php echo e(route('admin.create-subject')); ?>" method="POST" class="bg-gray-50 p-4 rounded">
        <?php echo csrf_field(); ?>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Subject Name</label>
                <input type="text" name="name" required placeholder="e.g., Mathematics, English" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                    Create Subject
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Subjects Table -->
<div>
    <h3 class="text-lg font-semibold mb-4">All Subjects</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Subject Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Teachers</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Students</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__currentLoopData = \App\Models\Subject::with(['subjectTeachers.user', 'grades'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            <?php echo e($subject->name); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            <?php if($subject->subjectTeachers->count() > 0): ?>
                                <?php $__currentLoopData = $subject->subjectTeachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                        <?php echo e($assignment->user->name); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="text-red-500">No teacher assigned</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            <?php echo e($subject->grades->count()); ?> grade records
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <form action="<?php echo e(route('admin.delete', ['type' => 'subject', 'id' => $subject->id])); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" onclick="return confirm('Are you sure? This will delete all grades for this subject.')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/subjects_simple.blade.php ENDPATH**/ ?>