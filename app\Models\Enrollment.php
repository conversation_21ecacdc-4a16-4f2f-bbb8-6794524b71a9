<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Enrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'class_id',
        'stream_id',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class, 'stream_id');
    }

    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class, 'enrollment_id');
    }

    // Helper methods
    public function getClassStreamAttribute(): string
    {
        $className = $this->schoolClass ? $this->schoolClass->name : 'Unknown Class';
        $streamName = $this->stream ? $this->stream->name : 'Unknown Stream';
        return $className . ' ' . $streamName;
    }

    public function getAverageGradeAttribute()
    {
        return $this->grades()->avg('grade');
    }

    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    public function scopeForStream($query, $streamId)
    {
        return $query->where('stream_id', $streamId);
    }
}
