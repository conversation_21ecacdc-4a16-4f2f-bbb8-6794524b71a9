<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Enrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'class_id',
        'stream_id',
        'academic_year',
    ];

    protected $casts = [
        'academic_year' => 'integer',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class, 'stream_id');
    }

    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class, 'enrollment_id');
    }

    // Helper methods
    public function getClassStreamAttribute(): string
    {
        $className = $this->schoolClass ? $this->schoolClass->name : 'Unknown Class';
        $streamName = $this->stream ? $this->stream->name : 'Unknown Stream';
        return $className . ' ' . $streamName;
    }

    public function getTermGrades(int $term)
    {
        return $this->grades()->where('term', $term)->with('subject')->get();
    }

    public function getTermAverageAttribute()
    {
        return $this->grades()->avg('total');
    }

    // Scopes
    public function scopeForAcademicYear($query, $year)
    {
        return $query->where('academic_year', $year);
    }

    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    public function scopeForStream($query, $streamId)
    {
        return $query->where('stream_id', $streamId);
    }
}
