<?php

namespace App\Http\Middleware;

use App\Models\ClassTeacher;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ClassTeacherMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Admin can access everything
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check if user is a class teacher
        if (!$user->isTeacher()) {
            abort(403, 'Access denied. Teacher privileges required.');
        }

        $isClassTeacher = ClassTeacher::where('user_id', $user->id)
            ->where('academic_year', date('Y'))
            ->exists();

        if (!$isClassTeacher) {
            abort(403, 'Access denied. Class teacher privileges required.');
        }

        return $next($request);
    }
}
