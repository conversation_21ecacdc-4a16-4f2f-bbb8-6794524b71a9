<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\Grade;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $student = auth()->user();

        // Get current enrollment
        $enrollment = Enrollment::with(['schoolClass', 'stream'])
            ->where('user_id', $student->id)
            ->first();

        if (!$enrollment) {
            return view('student.dashboard', [
                'enrollment' => null,
                'grades' => collect(),
                'stats' => ['message' => 'You are not enrolled in any class.']
            ]);
        }

        // Get grades for current enrollment
        $grades = Grade::with(['subject', 'teacher'])
            ->where('enrollment_id', $enrollment->id)
            ->orderBy('subject_id')
            ->get();

        $stats = [
            'class_stream' => $enrollment->class_stream,
            'total_subjects' => $grades->count(),
            'average_grade' => $grades->avg('grade'),
        ];

        return view('student.dashboard', compact('enrollment', 'grades', 'stats'));
    }


}
