<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\Grade;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $student = auth()->user();

        // Get current enrollment
        $enrollment = Enrollment::with(['schoolClass', 'stream'])
            ->where('user_id', $student->id)
            ->where('academic_year', date('Y'))
            ->first();

        if (!$enrollment) {
            return view('student.dashboard', [
                'enrollment' => null,
                'grades' => collect(),
                'stats' => ['message' => 'You are not enrolled in any class for this academic year.']
            ]);
        }

        // Get grades for current academic year
        $grades = Grade::with(['subject', 'teacher'])
            ->where('enrollment_id', $enrollment->id)
            ->orderBy('term')
            ->orderBy('subject_id')
            ->get();

        $stats = [
            'class_stream' => $enrollment->class_stream,
            'total_subjects' => $grades->groupBy('subject_id')->count(),
            'average_grade' => $grades->avg('total'),
            'current_term' => $this->getCurrentTerm(),
        ];

        return view('student.dashboard', compact('enrollment', 'grades', 'stats'));
    }

    private function getCurrentTerm()
    {
        $month = date('n');
        if ($month <= 4) return 1;
        if ($month <= 8) return 2;
        return 3;
    }
}
