<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Student Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if($enrollment)
                <!-- Student Info -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Class Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $stats['class_stream'] }}</div>
                                <div class="text-sm text-gray-600">Class & Stream</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">{{ $stats['total_subjects'] }}</div>
                                <div class="text-sm text-gray-600">Total Subjects</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">
                                    {{ $stats['average_grade'] ? number_format($stats['average_grade'], 1) . '%' : 'N/A' }}
                                </div>
                                <div class="text-sm text-gray-600">Average Grade</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-600">Term {{ $stats['current_term'] }}</div>
                                <div class="text-sm text-gray-600">Current Term</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grades by Term -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Grades</h3>
                        @if($grades->count() > 0)
                            <div class="bg-blue-50 p-3 rounded mb-4">
                                <p class="text-sm text-blue-700">
                                    📊 Your grades are organized by term. Teachers will update grades as they become available.
                                    Grades marked as "Not assigned" are waiting for teacher assignment.
                                </p>
                            </div>
                            @php
                                $gradesByTerm = $grades->groupBy('term');
                            @endphp

                            @foreach([1, 2, 3] as $term)
                                @if($gradesByTerm->has($term))
                                    <div class="mb-8">
                                        <h4 class="text-md font-semibold mb-3 text-gray-700">Term {{ $term }}</h4>
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full table-auto">
                                                <thead>
                                                    <tr class="bg-gray-50">
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignment (30%)</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Midterm (30%)</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final (40%)</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    @foreach($gradesByTerm[$term] as $grade)
                                                        <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                {{ $grade->subject ? $grade->subject->name : 'Unknown Subject' }}
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                {{ $grade->assignment }}%
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                {{ $grade->midterm }}%
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                {{ $grade->final }}%
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                                    @if($grade->total >= 80) bg-green-100 text-green-800
                                                                    @elseif($grade->total >= 60) bg-yellow-100 text-yellow-800
                                                                    @else bg-red-100 text-red-800 @endif">
                                                                    {{ number_format($grade->total, 1) }}%
                                                                </span>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                <span class="font-semibold">{{ $grade->letter_grade }}</span>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                @if($grade->teacher)
                                                                    {{ $grade->teacher->name }}
                                                                @else
                                                                    <span class="text-gray-400 italic">Not assigned</span>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        @else
                            <p class="text-gray-500">No grades available yet. Grades will appear here once your teachers enter them.</p>
                        @endif
                    </div>
                </div>
            @else
                <!-- Not Enrolled Message -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Not Enrolled</h3>
                            <p class="mt-1 text-sm text-gray-500">{{ $stats['message'] }}</p>
                            <p class="mt-1 text-sm text-gray-500">Please contact the school administrator for enrollment.</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
