<!-- Enroll Student Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Enroll Student</h3>
    <form action="{{ route('admin.enroll-student') }}" method="POST" class="bg-gray-50 p-4 rounded">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Student</label>
                <select name="user_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Student</option>
                    @foreach(\App\Models\User::students()->get() as $student)
                        <option value="{{ $student->id }}">{{ $student->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Class</label>
                <select name="class_id" id="enrollment_class_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Class</option>
                    @foreach(\App\Models\SchoolClass::all() as $class)
                        <option value="{{ $class->id }}">{{ $class->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Stream</label>
                <select name="stream_id" id="enrollment_stream_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">First select class</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                    Enroll Student
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Enrollments Table -->
<div>
    <h3 class="text-lg font-semibold mb-4">All Enrollments</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Student Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Class</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Stream</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Grades</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Average</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach(\App\Models\Enrollment::with(['user', 'schoolClass', 'stream', 'grades'])->orderBy('created_at', 'desc')->get() as $enrollment)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            {{ $enrollment->user->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $enrollment->schoolClass->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $enrollment->stream->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $enrollment->grades->count() }} subjects
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            @php
                                $average = $enrollment->grades->avg('grade');
                            @endphp
                            @if($average > 0)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    @if($average >= 80) bg-green-100 text-green-800
                                    @elseif($average >= 60) bg-yellow-100 text-yellow-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ number_format($average, 1) }}%
                                </span>
                            @else
                                <span class="text-gray-400">No grades</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <form action="{{ route('admin.delete', ['type' => 'enrollment', 'id' => $enrollment->id]) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('Are you sure? This will delete all grades for this student.')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Remove
                                </button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<script>
// Load streams when class is selected
document.getElementById('enrollment_class_id').addEventListener('change', function() {
    const classId = this.value;
    const streamSelect = document.getElementById('enrollment_stream_id');
    
    streamSelect.innerHTML = '<option value="">Loading...</option>';
    
    if (classId) {
        const classData = @json(\App\Models\SchoolClass::with('streams')->get());
        const selectedClass = classData.find(c => c.id == classId);
        
        streamSelect.innerHTML = '<option value="">Choose Stream</option>';
        
        if (selectedClass && selectedClass.streams) {
            selectedClass.streams.forEach(stream => {
                const option = document.createElement('option');
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
            });
        }
    } else {
        streamSelect.innerHTML = '<option value="">First select class</option>';
    }
});
</script>
