<?php

namespace App\Http\Controllers\ClassTeacher;

use App\Http\Controllers\Controller;
use App\Models\ClassTeacher;
use App\Models\Enrollment;
use App\Models\Grade;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $teacher = auth()->user();

        // Get class teacher assignment
        $classTeacher = ClassTeacher::with(['schoolClass', 'stream'])
            ->where('user_id', $teacher->id)
            ->where('academic_year', date('Y'))
            ->first();

        if (!$classTeacher) {
            return view('class-teacher.dashboard', [
                'classTeacher' => null,
                'students' => collect(),
                'stats' => ['message' => 'You are not assigned as a class teacher for this academic year.']
            ]);
        }

        // Get students in this class
        $students = Enrollment::with(['user', 'grades.subject'])
            ->where('class_id', $classTeacher->class_id)
            ->where('stream_id', $classTeacher->stream_id)
            ->where('academic_year', date('Y'))
            ->get();

        $stats = [
            'class_stream' => $classTeacher->class_stream,
            'total_students' => $students->count(),
            'students_with_grades' => $students->filter(function($student) {
                return $student->grades->count() > 0;
            })->count(),
            'class_average' => $this->calculateClassAverage($students),
        ];

        return view('class-teacher.dashboard', compact('classTeacher', 'students', 'stats'));
    }

    private function calculateClassAverage($students)
    {
        $totalGrades = 0;
        $gradeCount = 0;

        foreach ($students as $student) {
            foreach ($student->grades as $grade) {
                $totalGrades += $grade->total;
                $gradeCount++;
            }
        }

        return $gradeCount > 0 ? $totalGrades / $gradeCount : 0;
    }
}
