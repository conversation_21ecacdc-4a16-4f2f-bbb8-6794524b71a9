<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Class Teacher Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if($classTeacher)
                <!-- Class Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Class: {{ $stats['class_stream'] }}</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600">{{ $stats['total_students'] }}</div>
                                <div class="text-sm text-gray-600">Total Students</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600">{{ $stats['students_with_grades'] }}</div>
                                <div class="text-sm text-gray-600">Students with Grades</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-purple-600">
                                    {{ $stats['class_average'] ? number_format($stats['class_average'], 1) . '%' : 'N/A' }}
                                </div>
                                <div class="text-sm text-gray-600">Class Average</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-orange-600">{{ date('Y') }}</div>
                                <div class="text-sm text-gray-600">Academic Year</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students List -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">My Students</h3>
                        @if($students->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full table-auto">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student Name</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subjects</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Grade</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($students as $student)
                                            @php
                                                $studentAverage = $student->grades->avg('total');
                                                $subjectCount = $student->grades->groupBy('subject_id')->count();
                                            @endphp
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ $student->user->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $student->user->email }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $subjectCount }} subjects
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    @if($studentAverage)
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                            @if($studentAverage >= 80) bg-green-100 text-green-800
                                                            @elseif($studentAverage >= 60) bg-yellow-100 text-yellow-800
                                                            @else bg-red-100 text-red-800 @endif">
                                                            {{ number_format($studentAverage, 1) }}%
                                                        </span>
                                                    @else
                                                        <span class="text-gray-400">No grades</span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    @if($studentAverage)
                                                        @if($studentAverage >= 80)
                                                            <span class="text-green-600 font-semibold">Excellent</span>
                                                        @elseif($studentAverage >= 70)
                                                            <span class="text-blue-600 font-semibold">Good</span>
                                                        @elseif($studentAverage >= 60)
                                                            <span class="text-yellow-600 font-semibold">Average</span>
                                                        @else
                                                            <span class="text-red-600 font-semibold">Needs Improvement</span>
                                                        @endif
                                                    @else
                                                        <span class="text-gray-400">Not assessed</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">No students enrolled in your class.</p>
                        @endif
                    </div>
                </div>
            @else
                <!-- Not Assigned Message -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h4m6 0h4M7 15h10" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Not Assigned as Class Teacher</h3>
                            <p class="mt-1 text-sm text-gray-500">{{ $stats['message'] }}</p>
                            <p class="mt-1 text-sm text-gray-500">Please contact the administrator for class teacher assignment.</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
