<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Enrollment: ') . $enrollment->user->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('admin.enrollments.update', $enrollment) }}">
                        @csrf
                        @method('PUT')

                        <!-- Student Selection -->
                        <div class="mb-4">
                            <label for="user_id" class="block text-sm font-medium text-gray-700">
                                Select Student
                            </label>
                            <select name="user_id" 
                                    id="user_id" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('user_id') border-red-500 @enderror"
                                    required>
                                <option value="">Choose a student...</option>
                                @foreach($students as $student)
                                    <option value="{{ $student->id }}" {{ old('user_id', $enrollment->user_id) == $student->id ? 'selected' : '' }}>
                                        {{ $student->name }} ({{ $student->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Class Selection -->
                        <div class="mb-4">
                            <label for="class_id" class="block text-sm font-medium text-gray-700">
                                Select Class
                            </label>
                            <select name="class_id" 
                                    id="class_id" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('class_id') border-red-500 @enderror"
                                    required>
                                <option value="">Choose a class...</option>
                                @foreach($classes as $class)
                                    <option value="{{ $class->id }}" {{ old('class_id', $enrollment->class_id) == $class->id ? 'selected' : '' }}>
                                        {{ $class->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('class_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Stream Selection -->
                        <div class="mb-4">
                            <label for="stream_id" class="block text-sm font-medium text-gray-700">
                                Select Stream
                            </label>
                            <select name="stream_id" 
                                    id="stream_id" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('stream_id') border-red-500 @enderror"
                                    required>
                                <option value="">First select a class...</option>
                            </select>
                            @error('stream_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academic Year -->
                        <div class="mb-4">
                            <label for="academic_year" class="block text-sm font-medium text-gray-700">
                                Academic Year
                            </label>
                            <input type="number" 
                                   name="academic_year" 
                                   id="academic_year" 
                                   value="{{ old('academic_year', $enrollment->academic_year) }}"
                                   min="2020" 
                                   max="2030"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('academic_year') border-red-500 @enderror"
                                   required>
                            @error('academic_year')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('admin.enrollments.index') }}" 
                               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Enrollment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('class_id').addEventListener('change', function() {
            const classId = this.value;
            const streamSelect = document.getElementById('stream_id');
            
            // Clear existing options
            streamSelect.innerHTML = '<option value="">Loading...</option>';
            
            if (classId) {
                // Get streams for selected class
                const classData = @json($classes);
                const selectedClass = classData.find(c => c.id == classId);
                
                streamSelect.innerHTML = '<option value="">Choose a stream...</option>';
                
                if (selectedClass && selectedClass.streams) {
                    selectedClass.streams.forEach(stream => {
                        const option = document.createElement('option');
                        option.value = stream.id;
                        option.textContent = stream.name;
                        if ('{{ old('stream_id', $enrollment->stream_id) }}' == stream.id) {
                            option.selected = true;
                        }
                        streamSelect.appendChild(option);
                    });
                }
            } else {
                streamSelect.innerHTML = '<option value="">First select a class...</option>';
            }
        });

        // Trigger change event on page load if class is already selected
        document.addEventListener('DOMContentLoaded', function() {
            const classSelect = document.getElementById('class_id');
            if (classSelect.value) {
                classSelect.dispatchEvent(new Event('change'));
            }
        });
    </script>
</x-app-layout>
