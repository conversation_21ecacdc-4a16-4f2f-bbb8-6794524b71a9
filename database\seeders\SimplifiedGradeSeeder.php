<?php

namespace Database\Seeders;

use App\Models\Enrollment;
use App\Models\Grade;
use App\Models\SubjectTeacher;
use Illuminate\Database\Seeder;

class SimplifiedGradeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all enrollments and subject teachers
        $enrollments = Enrollment::with('user')->get();
        $subjectTeachers = SubjectTeacher::with(['user', 'subject'])->get();

        foreach ($enrollments as $enrollment) {
            // Create grades for all subjects (simplified - one grade per subject)
            foreach ($subjectTeachers as $subjectTeacher) {
                Grade::create([
                    'enrollment_id' => $enrollment->id,
                    'subject_id' => $subjectTeacher->subject->id,
                    'teacher_id' => $subjectTeacher->user->id,
                    'grade' => rand(60, 95), // Random grade between 60-95
                ]);
            }
        }
    }
}
