<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    // Relationships
    public function subjectTeachers(): HasMany
    {
        return $this->hasMany(SubjectTeacher::class, 'subject_id');
    }

    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class, 'subject_id');
    }

    // Helper methods
    public function getTeachersAttribute()
    {
        return User::whereHas('subjectTeachers', function ($query) {
            $query->where('subject_id', $this->id);
        })->get();
    }

    public function getAverageGradeAttribute()
    {
        return $this->grades()->avg('grade');
    }
}
