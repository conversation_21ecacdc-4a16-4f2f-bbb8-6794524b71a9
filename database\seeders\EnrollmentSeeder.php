<?php

namespace Database\Seeders;

use App\Models\Enrollment;
use App\Models\SchoolClass;
use App\Models\Stream;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $students = User::where('role', 'student')->get();
        $classes = SchoolClass::with('streams')->get();
        $currentYear = date('Y');

        // Enroll students in different classes and streams
        $studentIndex = 0;

        foreach ($classes as $class) {
            foreach ($class->streams as $stream) {
                // Enroll 2 students per stream (adjust as needed)
                for ($i = 0; $i < 2 && $studentIndex < $students->count(); $i++) {
                    Enrollment::create([
                        'user_id' => $students[$studentIndex]->id,
                        'class_id' => $class->id,
                        'stream_id' => $stream->id,
                    ]);
                    $studentIndex++;
                }
            }
        }
    }
}
