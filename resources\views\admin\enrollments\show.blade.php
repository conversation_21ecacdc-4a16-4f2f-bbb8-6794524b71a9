<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Enrollment Details: ') . $enrollment->user->name }}
            </h2>
            <div class="space-x-2">
                <a href="{{ route('admin.enrollments.edit', $enrollment) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Enrollment
                </a>
                <a href="{{ route('admin.enrollments.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Enrollments
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Enrollment Info -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Enrollment Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-blue-50 p-4 rounded">
                            <h4 class="font-semibold text-blue-800">Student</h4>
                            <p class="text-lg font-bold text-blue-600">{{ $enrollment->user->name }}</p>
                            <p class="text-sm text-blue-500">{{ $enrollment->user->email }}</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded">
                            <h4 class="font-semibold text-green-800">Class</h4>
                            <p class="text-lg font-bold text-green-600">{{ $enrollment->schoolClass->name }}</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded">
                            <h4 class="font-semibold text-purple-800">Stream</h4>
                            <p class="text-lg font-bold text-purple-600">{{ $enrollment->stream->name }}</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded">
                            <h4 class="font-semibold text-orange-800">Academic Year</h4>
                            <p class="text-lg font-bold text-orange-600">{{ $enrollment->academic_year }}</p>
                        </div>
                    </div>
                    
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-semibold text-gray-800">Full Class Name</h4>
                            <p class="text-lg font-bold text-gray-600">{{ $enrollment->schoolClass->name }} {{ $enrollment->stream->name }}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-semibold text-gray-800">Enrollment Date</h4>
                            <p class="text-lg font-bold text-gray-600">{{ $enrollment->created_at ? $enrollment->created_at->format('M d, Y') : 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Grades -->
            @if($enrollment->grades->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Student Grades</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Subject
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Term
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assignment
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Midterm
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Final
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($enrollment->grades as $grade)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $grade->subject->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            Term {{ $grade->term }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $grade->assignment }}%
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $grade->midterm }}%
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $grade->final }}%
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($grade->total >= 80) bg-green-100 text-green-800
                                                @elseif($grade->total >= 60) bg-yellow-100 text-yellow-800
                                                @else bg-red-100 text-red-800
                                                @endif">
                                                {{ number_format($grade->total, 1) }}%
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @else
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 text-center">
                    <h3 class="text-lg font-semibold mb-2">No Grades Yet</h3>
                    <p class="text-gray-600">This student doesn't have any grades recorded yet.</p>
                </div>
            </div>
            @endif

        </div>
    </div>
</x-app-layout>
