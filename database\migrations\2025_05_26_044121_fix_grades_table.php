<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('grades', function (Blueprint $table) {
            $table->foreignId('enrollment_id')->after('id')->constrained();
            $table->foreignId('subject_id')->after('enrollment_id')->constrained();
            $table->foreignId('teacher_id')->after('subject_id')->constrained('users');
            $table->tinyInteger('term')->after('teacher_id')->default(1);
            $table->decimal('assignment', 5, 2)->after('term')->default(0);
            $table->decimal('midterm', 5, 2)->after('assignment')->default(0);
            $table->decimal('final', 5, 2)->after('midterm')->default(0);
        });

        // Add generated column for total
        DB::statement('ALTER TABLE grades ADD COLUMN total DECIMAL(5,2)
            GENERATED ALWAYS AS (assignment*0.3 + midterm*0.3 + final*0.4) STORED');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('grades', function (Blueprint $table) {
            //
        });
    }
};
