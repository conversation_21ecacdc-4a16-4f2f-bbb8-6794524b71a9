<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Enrollment;
use App\Models\Stream;
use App\Models\SubjectTeacher;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_students' => User::students()->count(),
            'total_teachers' => User::teachers()->count(),
            'total_classes' => SchoolClass::count(),
            'total_subjects' => Subject::count(),
            'current_enrollments' => Enrollment::count(),
        ];

        // Get system status alerts
        $alerts = $this->getSystemAlerts();

        return view('admin.dashboard', compact('stats', 'alerts'));
    }

    private function getSystemAlerts()
    {
        $alerts = [];

        // Check for subjects without teachers
        $subjectsWithoutTeachers = Subject::whereDoesntHave('subjectTeachers')->get();

        if ($subjectsWithoutTeachers->count() > 0) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Subjects Missing Teachers',
                'message' => $subjectsWithoutTeachers->count() . ' subjects need teachers assigned.',
                'action' => 'Go to Assignments tab to assign subject teachers.',
                'subjects' => $subjectsWithoutTeachers->pluck('name')->toArray()
            ];
        }

        // Check for students without grades
        $studentsWithoutGrades = User::students()
            ->whereDoesntHave('enrollments.grades')
            ->whereHas('enrollments')
            ->get();

        if ($studentsWithoutGrades->count() > 0) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Students Need Grades',
                'message' => $studentsWithoutGrades->count() . ' enrolled students have no grades yet.',
                'action' => 'Teachers should add grades for these students.',
                'students' => $studentsWithoutGrades->pluck('name')->take(5)->toArray()
            ];
        }

        // Check for subjects without teachers
        $subjectsWithoutTeachers = Subject::whereDoesntHave('subjectTeachers')->get();

        if ($subjectsWithoutTeachers->count() > 0) {
            $alerts[] = [
                'type' => 'error',
                'title' => 'Subjects Missing Teachers',
                'message' => $subjectsWithoutTeachers->count() . ' subjects have no teachers assigned.',
                'action' => 'Go to Assignments tab to assign teachers to subjects.',
                'subjects' => $subjectsWithoutTeachers->pluck('name')->toArray()
            ];
        }

        // Check if we have the 5 core subjects
        $coreSubjects = ['Mathematics', 'English', 'Kiswahili', 'Science', 'Social Studies'];
        $existingSubjects = Subject::pluck('name')->toArray();
        $missingSubjects = array_diff($coreSubjects, $existingSubjects);

        if (!empty($missingSubjects)) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Missing Core Subjects',
                'message' => 'Some core subjects are missing: ' . implode(', ', $missingSubjects),
                'action' => 'Go to Subjects tab to add missing subjects.',
                'missing' => $missingSubjects
            ];
        }

        return $alerts;
    }

    // Simple CRUD operations - all in one controller
    public function createUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'role' => 'required|in:admin,teacher,student',
            'password' => 'required|min:8',
            // Conditional validation for students
            'class_id' => 'required_if:role,student|exists:classes,id',
            'stream_id' => 'required_if:role,student|exists:streams,id',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => Hash::make($request->password),
        ]);

        // Auto-assign based on role
        if ($request->role === 'student' && $request->class_id && $request->stream_id) {
            $enrollment = Enrollment::create([
                'user_id' => $user->id,
                'class_id' => $request->class_id,
                'stream_id' => $request->stream_id,
            ]);

            // Auto-create grade records for all subjects
            $this->createGradeRecordsForStudent($enrollment);
        }

        if ($request->role === 'teacher' && $request->subject_ids) {
            foreach ($request->subject_ids as $subjectId) {
                SubjectTeacher::create([
                    'user_id' => $user->id,
                    'subject_id' => $subjectId,
                ]);
            }
        }

        // Create success message based on role
        $message = 'User created successfully!';
        if ($request->role === 'student' && $request->class_id && $request->stream_id) {
            $className = SchoolClass::find($request->class_id)->name;
            $streamName = Stream::find($request->stream_id)->name;
            $subjectCount = Subject::count();
            $message = "Student created and enrolled in {$className} {$streamName}! Auto-created {$subjectCount} subjects × 3 terms = " . ($subjectCount * 3) . " grade records.";
        } elseif ($request->role === 'teacher' && $request->subject_ids) {
            $subjectCount = count($request->subject_ids);
            $message = "Teacher created and assigned to {$subjectCount} subjects!";
        }

        return back()->with('success', $message);
    }

    public function createClass(Request $request)
    {
        $request->validate(['name' => 'required|unique:classes']);
        SchoolClass::create(['name' => $request->name]);
        return back()->with('success', 'Class created successfully!');
    }

    public function createStream(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => 'required|string|max:10',
        ]);
        Stream::create([
            'class_id' => $request->class_id,
            'name' => $request->name,
        ]);
        return back()->with('success', 'Stream created successfully!');
    }

    public function createSubject(Request $request)
    {
        $request->validate(['name' => 'required|unique:subjects']);
        Subject::create(['name' => $request->name]);
        return back()->with('success', 'Subject created successfully!');
    }

    public function enrollStudent(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'stream_id' => 'required|exists:streams,id',
        ]);

        $enrollment = Enrollment::create([
            'user_id' => $request->user_id,
            'class_id' => $request->class_id,
            'stream_id' => $request->stream_id,
        ]);

        // Auto-create grade records for all subjects
        $this->createGradeRecordsForStudent($enrollment);

        return back()->with('success', 'Student enrolled successfully with all subjects!');
    }

    private function createGradeRecordsForStudent($enrollment)
    {
        // Get all subjects
        $subjects = Subject::all();

        // Create grade records for all subjects (one per subject)
        foreach ($subjects as $subject) {
            // Check if grade record already exists
            $existingGrade = \App\Models\Grade::where('enrollment_id', $enrollment->id)
                ->where('subject_id', $subject->id)
                ->first();

            if (!$existingGrade) {
                \App\Models\Grade::create([
                    'enrollment_id' => $enrollment->id,
                    'subject_id' => $subject->id,
                    'teacher_id' => null, // Will be set when teacher enters grades
                    'grade' => 0,
                ]);
            }
        }
    }

    public function assignTeacher(Request $request)
    {
        $request->validate([
            'teacher_id' => 'required|exists:users,id',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        SubjectTeacher::create([
            'user_id' => $request->teacher_id,
            'subject_id' => $request->subject_id,
        ]);
        return back()->with('success', 'Teacher assigned successfully!');
    }



    public function delete($type, $id)
    {
        switch ($type) {
            case 'user':
                User::findOrFail($id)->delete();
                break;
            case 'class':
                SchoolClass::findOrFail($id)->delete();
                break;
            case 'stream':
                Stream::findOrFail($id)->delete();
                break;
            case 'subject':
                Subject::findOrFail($id)->delete();
                break;
            case 'enrollment':
                Enrollment::findOrFail($id)->delete();
                break;
        }
        return back()->with('success', ucfirst($type) . ' deleted successfully!');
    }

    public function assignTeacherSubject(Request $request)
    {
        $request->validate([
            'teacher_id' => 'required|exists:users,id',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        SubjectTeacher::create([
            'user_id' => $request->teacher_id,
            'subject_id' => $request->subject_id,
        ]);
        return back()->with('success', 'Teacher assigned to subject successfully!');
    }

    public function removeTeacherSubject(Request $request)
    {
        SubjectTeacher::where('user_id', $request->teacher_id)
                     ->where('subject_id', $request->subject_id)
                     ->delete();
        return back()->with('success', 'Teacher removed from subject successfully!');
    }


}
