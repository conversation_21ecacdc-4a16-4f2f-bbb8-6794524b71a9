<!-- Add Class Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Add New Class</h3>
    <form action="{{ route('admin.create-class') }}" method="POST" class="bg-gray-50 p-4 rounded">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Class Name</label>
                <input type="text" name="name" required placeholder="e.g., Class 1, Class 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                    Create Class
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Add Stream Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Add New Stream</h3>
    <form action="{{ route('admin.create-stream') }}" method="POST" class="bg-gray-50 p-4 rounded">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Class</label>
                <select name="class_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Class</option>
                    @foreach(\App\Models\SchoolClass::all() as $class)
                        <option value="{{ $class->id }}">{{ $class->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Stream Name</label>
                <input type="text" name="name" required placeholder="e.g., A, B, C" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium">
                    Create Stream
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Classes and Streams Table -->
<div>
    <h3 class="text-lg font-semibold mb-4">All Classes and Streams</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Class</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Streams</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Students</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach(\App\Models\SchoolClass::with(['streams', 'enrollments'])->get() as $class)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            {{ $class->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            @if($class->streams->count() > 0)
                                @foreach($class->streams as $stream)
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                        {{ $stream->name }}
                                    </span>
                                @endforeach
                            @else
                                <span class="text-gray-400">No streams</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $class->enrollments->count() }} students
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <form action="{{ route('admin.delete', ['type' => 'class', 'id' => $class->id]) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('Are you sure? This will delete all streams and enrollments for this class.')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Delete Class
                                </button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- Individual Streams Table -->
<div class="mt-8">
    <h3 class="text-lg font-semibold mb-4">All Streams</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Class</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Stream</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Students</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach(\App\Models\Stream::with(['schoolClass', 'enrollments'])->get() as $stream)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $stream->schoolClass->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            {{ $stream->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            {{ $stream->enrollments->count() }} students
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <form action="{{ route('admin.delete', ['type' => 'stream', 'id' => $stream->id]) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" onclick="return confirm('Are you sure? This will delete all enrollments for this stream.')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Delete Stream
                                </button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
