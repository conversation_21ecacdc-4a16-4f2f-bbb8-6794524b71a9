<!-- Assign Teacher to Subject Form -->
<div class="mb-6">
    <h3 class="text-lg font-semibold mb-4">Assign Teacher to Subject</h3>
    <form action="<?php echo e(route('admin.assign-teacher-subject')); ?>" method="POST" class="bg-gray-50 p-4 rounded">
        <?php echo csrf_field(); ?>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Teacher</label>
                <select name="teacher_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Teacher</option>
                    <?php $__currentLoopData = \App\Models\User::teachers()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($teacher->id); ?>"><?php echo e($teacher->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Select Subject</label>
                <select name="subject_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Choose Subject</option>
                    <?php $__currentLoopData = \App\Models\Subject::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($subject->id); ?>"><?php echo e($subject->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium">
                    Assign Teacher
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Teacher Assignments Table -->
<div>
    <h3 class="text-lg font-semibold mb-4">Teacher Subject Assignments</h3>
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Teacher Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Subject</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Students</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__currentLoopData = \App\Models\SubjectTeacher::with(['user', 'subject'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-b">
                            <?php echo e($assignment->user->name); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            <?php echo e($assignment->subject->name); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-b">
                            <?php
                                $studentCount = \App\Models\Grade::where('subject_id', $assignment->subject->id)->distinct('enrollment_id')->count();
                            ?>
                            <?php echo e($studentCount); ?> students
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium border-b">
                            <form action="<?php echo e(route('admin.remove-teacher-subject')); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="teacher_id" value="<?php echo e($assignment->user_id); ?>">
                                <input type="hidden" name="subject_id" value="<?php echo e($assignment->subject_id); ?>">
                                <button type="submit" onclick="return confirm('Are you sure?')" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs">
                                    Remove
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/assignments_simple.blade.php ENDPATH**/ ?>