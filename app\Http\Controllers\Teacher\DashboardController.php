<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\SubjectTeacher;
use App\Models\Grade;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $teacher = auth()->user();

        // Get subjects assigned to this teacher
        $assignedSubjects = SubjectTeacher::with('subject')
            ->where('user_id', $teacher->id)
            ->get();

        // Get recent grades entered by this teacher
        $recentGrades = Grade::with(['enrollment.user', 'subject'])
            ->where('teacher_id', $teacher->id)
            ->latest()
            ->take(10)
            ->get();

        $stats = [
            'assigned_subjects' => $assignedSubjects->count(),
            'total_grades_entered' => Grade::where('teacher_id', $teacher->id)->count(),
            'pending_grades' => 0, // TODO: Calculate pending grades
        ];

        return view('teacher.dashboard', compact('assignedSubjects', 'recentGrades', 'stats'));
    }
}
