<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Stream extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'class_id',
        'name',
    ];

    // Relationships
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    public function enrollments(): HasMany
    {
        return $this->hasMany(Enrollment::class, 'stream_id');
    }



    // Helper methods
    public function getFullNameAttribute(): string
    {
        return $this->schoolClass->name . ' ' . $this->name;
    }

    public function getStudentsAttribute()
    {
        return User::whereHas('enrollments', function ($query) {
            $query->where('class_id', $this->class_id)
                  ->where('stream_id', $this->id);
        })->get();
    }
}
