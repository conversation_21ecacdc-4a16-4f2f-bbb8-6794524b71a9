<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SchoolClass extends Model
{
    use HasFactory;

    protected $table = 'classes';

    protected $fillable = [
        'name',
    ];

    // Relationships
    public function streams(): HasMany
    {
        return $this->hasMany(Stream::class, 'class_id');
    }

    public function enrollments(): HasMany
    {
        return $this->hasMany(Enrollment::class, 'class_id');
    }



    // Helper methods
    public function getStudentsAttribute()
    {
        return User::whereHas('enrollments', function ($query) {
            $query->where('class_id', $this->id);
        })->get();
    }
}
