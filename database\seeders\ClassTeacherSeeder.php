<?php

namespace Database\Seeders;

use App\Models\ClassTeacher;
use App\Models\Stream;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ClassTeacherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teachers = User::teachers()->take(6)->get(); // Get first 6 teachers
        $streams = Stream::with('schoolClass')->take(6)->get(); // Get first 6 streams

        // Assign each teacher to a class-stream
        foreach ($teachers as $index => $teacher) {
            if (isset($streams[$index])) {
                ClassTeacher::create([
                    'user_id' => $teacher->id,
                    'class_id' => $streams[$index]->class_id,
                    'stream_id' => $streams[$index]->id,
                    'academic_year' => date('Y'),
                ]);
            }
        }
    }
}
