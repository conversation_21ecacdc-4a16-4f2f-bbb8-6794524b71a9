<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Stream;
use App\Models\Subject;
use App\Models\Enrollment;
use App\Models\SubjectTeacher;
use App\Models\ClassTeacher;
use Illuminate\Support\Facades\Hash;

class DemoSeeder extends Seeder
{
    /**
     * Run the database seeds for demo purposes.
     */
    public function run(): void
    {
        // Create demo admin
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Create demo classes
        $class1 = SchoolClass::create(['name' => 'Class 1']);
        $class2 = SchoolClass::create(['name' => 'Class 2']);

        // Create demo streams
        $class1A = Stream::create(['class_id' => $class1->id, 'name' => 'A']);
        $class1B = Stream::create(['class_id' => $class1->id, 'name' => 'B']);
        $class2A = Stream::create(['class_id' => $class2->id, 'name' => 'A']);

        // Create 5 core subjects only
        $math = Subject::create(['name' => 'Mathematics']);
        $english = Subject::create(['name' => 'English']);
        $kiswahili = Subject::create(['name' => 'Kiswahili']);
        $science = Subject::create(['name' => 'Science']);
        $social = Subject::create(['name' => 'Social Studies']);

        // Create demo teachers
        $teacher1 = User::create([
            'name' => 'John Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'teacher',
        ]);

        $teacher2 = User::create([
            'name' => 'Jane Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'teacher',
        ]);

        // Create demo students
        $student1 = User::create([
            'name' => 'Alice Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
        ]);

        $student2 = User::create([
            'name' => 'Bob Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
        ]);

        $student3 = User::create([
            'name' => 'Charlie Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
        ]);

        // Assign teachers to subjects
        SubjectTeacher::create(['user_id' => $teacher1->id, 'subject_id' => $math->id]);
        SubjectTeacher::create(['user_id' => $teacher1->id, 'subject_id' => $science->id]);
        SubjectTeacher::create(['user_id' => $teacher1->id, 'subject_id' => $social->id]);
        SubjectTeacher::create(['user_id' => $teacher2->id, 'subject_id' => $english->id]);
        SubjectTeacher::create(['user_id' => $teacher2->id, 'subject_id' => $kiswahili->id]);

        // Assign class teachers
        ClassTeacher::create([
            'user_id' => $teacher1->id,
            'class_id' => $class1->id,
            'stream_id' => $class1A->id,
            'academic_year' => date('Y'),
        ]);

        // Enroll students
        Enrollment::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'stream_id' => $class1A->id,
            'academic_year' => date('Y'),
        ]);

        Enrollment::create([
            'user_id' => $student2->id,
            'class_id' => $class1->id,
            'stream_id' => $class1A->id,
            'academic_year' => date('Y'),
        ]);

        Enrollment::create([
            'user_id' => $student3->id,
            'class_id' => $class2->id,
            'stream_id' => $class2A->id,
            'academic_year' => date('Y'),
        ]);

        echo "Demo data created successfully!\n";
        echo "Login credentials:\n";
        echo "Admin: <EMAIL> / password\n";
        echo "Teacher: <EMAIL> / password\n";
        echo "Student: <EMAIL> / password\n";
    }
}
