<!-- Student Enrollments Tab -->
<div class="space-y-6">
    <!-- Enroll Student Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Enroll Student</h3>
        <p class="text-sm text-gray-600 mb-4">✨ When you enroll a student, they automatically get grade records for all 5 subjects (Math, English, Kiswahili, Science, Social Studies)!</p>
        <form action="<?php echo e(route('admin.enroll-student')); ?>" method="POST" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Student</label>
                <select name="user_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">Choose Student</option>
                    <?php $__currentLoopData = \App\Models\User::students()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($student->id); ?>"><?php echo e($student->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Class</label>
                <select name="class_id" id="enrollment_class_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">Choose Class</option>
                    <?php $__currentLoopData = \App\Models\SchoolClass::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($class->id); ?>"><?php echo e($class->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Stream</label>
                <select name="stream_id" id="enrollment_stream_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">First select class</option>
                </select>
            </div>

            <div class="md:col-span-3">
                <button type="submit" class="bg-red-600 hover:bg-red-800 text-white font-bold py-3 px-6 rounded-lg shadow-lg border-2 border-red-700">
                    📝 Enroll Student
                </button>
            </div>
        </form>
    </div>

    <!-- Enrollments List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">Current Enrollments</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Student</th>
                        <th class="px-4 py-2 text-left">Class</th>
                        <th class="px-4 py-2 text-left">Stream</th>

                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = \App\Models\Enrollment::with(['user', 'schoolClass', 'stream', 'grades'])->orderBy('created_at', 'desc')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-t">
                            <td class="px-4 py-2">
                                <?php echo e($enrollment->user->name); ?>

                                <div class="text-xs text-green-600"><?php echo e($enrollment->grades->count()); ?> grade records</div>
                            </td>
                            <td class="px-4 py-2"><?php echo e($enrollment->schoolClass->name); ?></td>
                            <td class="px-4 py-2"><?php echo e($enrollment->stream->name); ?></td>

                            <td class="px-4 py-2">
                                <button onclick="editEnrollment(<?php echo e($enrollment->id); ?>, <?php echo e($enrollment->user_id); ?>, <?php echo e($enrollment->class_id); ?>, <?php echo e($enrollment->stream_id); ?>)" class="text-blue-600 hover:text-blue-900 mr-2">Edit</button>
                                <form action="<?php echo e(route('admin.delete', ['type' => 'enrollment', 'id' => $enrollment->id])); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900"
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button onclick="enrollAllStudents()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                Auto-Enroll All Students
            </button>
            <button onclick="showEnrollmentTips()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                Enrollment Tips
            </button>
            <button onclick="resetEnrollmentForm()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                Reset Form
            </button>
        </div>
    </div>
</div>

<script>
document.getElementById('enrollment_class_id').addEventListener('change', function() {
    const classId = this.value;
    const streamSelect = document.getElementById('enrollment_stream_id');

    streamSelect.innerHTML = '<option value="">Loading...</option>';

    if (classId) {
        const classData = <?php echo json_encode(\App\Models\SchoolClass::with('streams')->get(), 15, 512) ?>;
        const selectedClass = classData.find(c => c.id == classId);

        streamSelect.innerHTML = '<option value="">Choose Stream</option>';

        if (selectedClass && selectedClass.streams) {
            selectedClass.streams.forEach(stream => {
                const option = document.createElement('option');
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
            });
        }
    } else {
        streamSelect.innerHTML = '<option value="">First select class</option>';
    }
});

function enrollAllStudents() {
    alert('This would automatically enroll all students without class assignments.\n\nFor your school project, you can manually enroll students one by one using the form above.');
}

function showEnrollmentTips() {
    alert('Enrollment Tips:\n\n1. Select a student first\n2. Choose their class (Class 1, Class 2, etc.)\n3. Pick a stream (A, B, or C)\n4. Click "Enroll Student"\n\nStudents can only be enrolled in one class-stream combination.');
}

function resetEnrollmentForm() {
    document.querySelector('form').reset();
    document.getElementById('enrollment_stream_id').innerHTML = '<option value="">First select class</option>';
}

function editEnrollment(id, userId, classId, streamId) {
    document.querySelector('select[name="user_id"]').value = userId;
    document.getElementById('enrollment_class_id').value = classId;

    // Trigger class change to load streams
    document.getElementById('enrollment_class_id').dispatchEvent(new Event('change'));

    // Set stream after a short delay to allow streams to load
    setTimeout(() => {
        document.getElementById('enrollment_stream_id').value = streamId;
    }, 100);

    alert('Enrollment details loaded in form. Modify and submit to update.');
}
</script>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/enrollments.blade.php ENDPATH**/ ?>